package main

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// TestCounterInconsistencyFix tests the counter inconsistency fix in isolation
func TestCounterInconsistencyFix(t *testing.T) {
	// Save original state
	originalProcessingCount := atomic.LoadInt64(&processingCount)
	originalProcessingProps := processingProps
	originalTaskStatus := taskStatus

	// Reset for test
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	// Restore after test
	defer func() {
		atomic.StoreInt64(&processingCount, originalProcessingCount)
		processingProps = originalProcessingProps
		taskStatus = originalTaskStatus
	}()

	// Test case 1: Simulate inconsistency where atomic counter is higher than actual tasks
	t.Log("Testing counter inconsistency fix...")
	
	// Set atomic counter to 7 (simulating the issue you encountered)
	atomic.StoreInt64(&processingCount, 7)
	
	// But only add 2 actual tasks in processingProps
	processingProps.Store("TASK_1", time.Now())
	processingProps.Store("TASK_2", time.Now())
	
	// Verify inconsistency exists
	atomicCount := atomic.LoadInt64(&processingCount)
	if atomicCount != 7 {
		t.Errorf("Expected atomic count 7, got %d", atomicCount)
	}
	
	actualCount := 0
	processingProps.Range(func(key, value interface{}) bool {
		actualCount++
		return true
	})
	if actualCount != 2 {
		t.Errorf("Expected actual count 2, got %d", actualCount)
	}
	
	t.Logf("Before fix: atomic=%d, actual=%d", atomicCount, actualCount)
	
	// Fix the inconsistency
	fixedCount := fixCounterInconsistency()
	
	// Should have fixed 5 tasks (7 - 2 = 5)
	if fixedCount != 5 {
		t.Errorf("Expected to fix 5 inconsistencies, fixed %d", fixedCount)
	}
	
	// Counter should now match actual tasks
	newAtomicCount := atomic.LoadInt64(&processingCount)
	if newAtomicCount != 2 {
		t.Errorf("Expected atomic count 2 after fix, got %d", newAtomicCount)
	}
	
	t.Logf("After fix: atomic=%d, fixed=%d", newAtomicCount, fixedCount)
	
	// Test case 2: Counter is already consistent
	t.Log("Testing already consistent counter...")
	fixedCount = fixCounterInconsistency()
	if fixedCount != 0 {
		t.Errorf("Expected no fixes needed when consistent, got %d", fixedCount)
	}
	
	// Test case 3: Empty state
	t.Log("Testing empty state...")
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}
	
	fixedCount = fixCounterInconsistency()
	if fixedCount != 0 {
		t.Errorf("Expected no fixes needed for empty state, got %d", fixedCount)
	}
	
	finalCount := atomic.LoadInt64(&processingCount)
	if finalCount != 0 {
		t.Errorf("Expected final count 0, got %d", finalCount)
	}
	
	t.Log("Counter inconsistency fix test passed")
}

// TestCounterInconsistencyEdgeCases tests edge cases for the fix function
func TestCounterInconsistencyEdgeCases(t *testing.T) {
	// Save original state
	originalProcessingCount := atomic.LoadInt64(&processingCount)
	originalProcessingProps := processingProps
	originalTaskStatus := taskStatus

	// Reset for test
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	// Restore after test
	defer func() {
		atomic.StoreInt64(&processingCount, originalProcessingCount)
		processingProps = originalProcessingProps
		taskStatus = originalTaskStatus
	}()

	// Edge case 1: Counter is lower than actual tasks (shouldn't happen, but test anyway)
	t.Log("Testing counter lower than actual tasks...")
	atomic.StoreInt64(&processingCount, 1)
	processingProps.Store("TASK_1", time.Now())
	processingProps.Store("TASK_2", time.Now())
	processingProps.Store("TASK_3", time.Now())
	
	fixedCount := fixCounterInconsistency()
	// Should not fix anything since counter is lower than actual
	if fixedCount != 0 {
		t.Errorf("Expected no fixes when counter is lower, got %d", fixedCount)
	}
	
	// Counter should remain unchanged
	if atomic.LoadInt64(&processingCount) != 1 {
		t.Errorf("Expected counter to remain 1, got %d", atomic.LoadInt64(&processingCount))
	}
	
	// Edge case 2: Large inconsistency
	t.Log("Testing large inconsistency...")
	atomic.StoreInt64(&processingCount, 100)
	processingProps = sync.Map{} // Empty
	
	fixedCount = fixCounterInconsistency()
	if fixedCount != 100 {
		t.Errorf("Expected to fix 100 inconsistencies, fixed %d", fixedCount)
	}
	
	if atomic.LoadInt64(&processingCount) != 0 {
		t.Errorf("Expected counter to be 0 after fix, got %d", atomic.LoadInt64(&processingCount))
	}
	
	t.Log("Counter inconsistency edge cases test passed")
}
