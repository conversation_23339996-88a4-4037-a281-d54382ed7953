package main

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// TestCleanupLogic tests the cleanup logic in isolation
func TestCleanupLogic(t *testing.T) {
	// Save original state
	originalProcessingCount := atomic.LoadInt64(&processingCount)
	originalProcessingProps := processingProps
	originalTaskStatus := taskStatus

	// Reset for test
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	// Restore after test
	defer func() {
		atomic.StoreInt64(&processingCount, originalProcessingCount)
		processingProps = originalProcessingProps
		taskStatus = originalTaskStatus
	}()

	// Test basic task lifecycle
	propID := "TEST_PROP_123"
	
	// Start processing
	startProcessingProp(propID)
	
	// Verify task was started
	if count := atomic.LoadInt64(&processingCount); count != 1 {
		t.Errorf("Expected processing count 1, got %d", count)
	}
	
	// Verify task status exists
	if _, exists := taskStatus.Load(propID); !exists {
		t.Error("Task status should exist after starting")
	}
	
	// Update stage
	updateTaskStage(propID, StageDownloading)
	
	// Verify stage update
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		if status.Stage != StageDownloading {
			t.Errorf("Expected stage %s, got %s", StageDownloading, status.Stage)
		}
	} else {
		t.Error("Task status should exist after stage update")
	}
	
	// Should not be complete yet
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be complete yet")
	}
	
	// Finish processing
	finishProcessingProp(propID)
	
	// Verify cleanup
	if count := atomic.LoadInt64(&processingCount); count != 0 {
		t.Errorf("Expected processing count 0 after finish, got %d", count)
	}
	
	// Task status should be cleaned up
	if _, exists := taskStatus.Load(propID); exists {
		t.Error("Task status should be cleaned up after finish")
	}
	
	// Should be complete now
	if !isAllTasksFullyComplete() {
		t.Error("All tasks should be complete now")
	}
	
	t.Log("Basic cleanup logic test passed")
}

// TestStaleTaskCleanup tests the stale task cleanup functionality
func TestStaleTaskCleanup(t *testing.T) {
	// Save original state
	originalProcessingCount := atomic.LoadInt64(&processingCount)
	originalProcessingProps := processingProps
	originalTaskStatus := taskStatus

	// Reset for test
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	// Restore after test
	defer func() {
		atomic.StoreInt64(&processingCount, originalProcessingCount)
		processingProps = originalProcessingProps
		taskStatus = originalTaskStatus
	}()

	// Create tasks with different ages
	now := time.Now()
	tasks := []struct {
		propID string
		age    time.Duration
	}{
		{"STALE_1", 10 * time.Minute}, // Old task
		{"STALE_2", 2 * time.Minute},  // Newer task  
		{"FRESH_1", 30 * time.Second}, // Fresh task
	}

	// Start tasks and simulate different ages
	for _, task := range tasks {
		startProcessingProp(task.propID)
		
		// Manually adjust start time to simulate age
		if statusInterface, exists := taskStatus.Load(task.propID); exists {
			status := statusInterface.(*TaskStatus)
			status.StartTime = now.Add(-task.age)
			status.LastUpdate = now.Add(-task.age)
			taskStatus.Store(task.propID, status)
		}
		
		// Also adjust processingProps map
		processingProps.Store(task.propID, now.Add(-task.age))
	}

	// Verify initial state
	if count := atomic.LoadInt64(&processingCount); count != 3 {
		t.Errorf("Expected processing count 3, got %d", count)
	}

	// Clean up tasks older than 5 minutes
	cleanedCount := forceCleanupStaleTasks(5 * time.Minute)
	
	// Should have cleaned up STALE_1 (10 minutes old)
	if cleanedCount != 1 {
		t.Errorf("Expected to clean 1 task, cleaned %d", cleanedCount)
	}

	// Check remaining count
	if count := atomic.LoadInt64(&processingCount); count != 2 {
		t.Errorf("Expected processing count 2 after cleanup, got %d", count)
	}

	// Clean up all remaining tasks (use 0 duration to clean all)
	cleanedCount = forceCleanupStaleTasks(0)
	if cleanedCount != 2 {
		t.Errorf("Expected to clean 2 remaining tasks, cleaned %d", cleanedCount)
	}

	// Should be empty now
	if count := atomic.LoadInt64(&processingCount); count != 0 {
		t.Errorf("Expected processing count 0 after full cleanup, got %d", count)
	}

	if !isAllTasksFullyComplete() {
		t.Error("All tasks should be complete after cleanup")
	}

	t.Log("Stale task cleanup test passed")
}
