package main

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// Test constants and types
const (
	testStageAnalyzing     = "analyzing"
	testStageDownloading   = "downloading"
	testStageUpdatingDB    = "updating_db"
	testStageRemovingQueue = "removing_queue"
	testStageCompleted     = "completed"
)

type TestTaskStatus struct {
	PropID      string
	StartTime   time.Time
	Stage       string
	LastUpdate  time.Time
}

// Isolated test for task tracking functions
func TestTaskTrackingFunctions(t *testing.T) {
	// Create isolated variables for this test
	var testProcessingCount int64
	testProcessingProps := sync.Map{}
	testTaskStatus := sync.Map{}

	// Test functions that work with our isolated variables
	testStartProcessingProp := func(propID string) {
		atomic.AddInt64(&testProcessingCount, 1)
		testProcessingProps.Store(propID, time.Now())

		status := &TestTaskStatus{
			PropID:     propID,
			StartTime:  time.Now(),
			Stage:      testStageAnalyzing,
			LastUpdate: time.Now(),
		}
		testTaskStatus.Store(propID, status)
	}

	testUpdateTaskStage := func(propID string, stage string) {
		if statusInterface, exists := testTaskStatus.Load(propID); exists {
			status := statusInterface.(*TestTaskStatus)
			status.Stage = stage
			status.LastUpdate = time.Now()
			testTaskStatus.Store(propID, status)
		}
	}

	testFinishProcessingProp := func(propID string) {
		if _, exists := testProcessingProps.LoadAndDelete(propID); exists {
			atomic.AddInt64(&testProcessingCount, -1)
			testUpdateTaskStage(propID, testStageCompleted)
			testTaskStatus.Delete(propID)
		}
	}

	testIsAllTasksFullyComplete := func() bool {
		activeCount := atomic.LoadInt64(&testProcessingCount)
		if activeCount > 0 {
			return false
		}

		allCompleted := true
		testTaskStatus.Range(func(key, value interface{}) bool {
			status := value.(*TestTaskStatus)
			if status.Stage != testStageCompleted {
				allCompleted = false
			}
			return true
		})

		return allCompleted
	}

	testForceCleanupStaleTasks := func(maxDuration time.Duration) int {
		cleanedCount := 0
		var staleTasks []string

		testTaskStatus.Range(func(key, value interface{}) bool {
			propID := key.(string)
			status := value.(*TestTaskStatus)

			if time.Since(status.StartTime) > maxDuration {
				staleTasks = append(staleTasks, propID)
			}
			return true
		})

		for _, propID := range staleTasks {
			if _, exists := testProcessingProps.LoadAndDelete(propID); exists {
				atomic.AddInt64(&testProcessingCount, -1)
				cleanedCount++
			}
			testTaskStatus.Delete(propID)
		}

		return cleanedCount
	}

	// Test basic lifecycle
	propID := "TEST_PROP_123"

	// Start processing
	testStartProcessingProp(propID)

	// Verify task was started
	if count := atomic.LoadInt64(&testProcessingCount); count != 1 {
		t.Errorf("Expected processing count 1, got %d", count)
	}

	// Update stage
	testUpdateTaskStage(propID, testStageDownloading)

	// Should not be complete yet
	if testIsAllTasksFullyComplete() {
		t.Error("Tasks should not be complete yet")
	}

	// Finish processing
	testFinishProcessingProp(propID)

	// Verify cleanup
	if count := atomic.LoadInt64(&testProcessingCount); count != 0 {
		t.Errorf("Expected processing count 0 after finish, got %d", count)
	}

	// Should be complete now
	if !testIsAllTasksFullyComplete() {
		t.Error("All tasks should be complete now")
	}

	// Test stale task cleanup
	now := time.Now()
	tasks := []struct {
		propID string
		age    time.Duration
	}{
		{"STALE_1", 10 * time.Minute},
		{"STALE_2", 2 * time.Minute},
		{"FRESH_1", 30 * time.Second},
	}

	// Start tasks and simulate different ages
	for _, task := range tasks {
		testStartProcessingProp(task.propID)

		// Manually adjust start time
		if statusInterface, exists := testTaskStatus.Load(task.propID); exists {
			status := statusInterface.(*TestTaskStatus)
			status.StartTime = now.Add(-task.age)
			status.LastUpdate = now.Add(-task.age)
			testTaskStatus.Store(task.propID, status)
		}

		testProcessingProps.Store(task.propID, now.Add(-task.age))
	}

	// Verify initial state
	if count := atomic.LoadInt64(&testProcessingCount); count != 3 {
		t.Errorf("Expected processing count 3, got %d", count)
	}

	// Clean up tasks older than 5 minutes
	cleanedCount := testForceCleanupStaleTasks(5 * time.Minute)

	// Should have cleaned up STALE_1 (10 minutes old)
	if cleanedCount != 1 {
		t.Errorf("Expected to clean 1 task, cleaned %d", cleanedCount)
	}

	// Check remaining count
	if count := atomic.LoadInt64(&testProcessingCount); count != 2 {
		t.Errorf("Expected processing count 2 after cleanup, got %d", count)
	}

	// Clean up all remaining tasks
	cleanedCount = testForceCleanupStaleTasks(0)
	if cleanedCount != 2 {
		t.Errorf("Expected to clean 2 remaining tasks, cleaned %d", cleanedCount)
	}

	// Should be empty now
	if count := atomic.LoadInt64(&testProcessingCount); count != 0 {
		t.Errorf("Expected processing count 0 after full cleanup, got %d", count)
	}

	if !testIsAllTasksFullyComplete() {
		t.Error("All tasks should be complete after cleanup")
	}

	t.Log("Task tracking functions test passed")
}
