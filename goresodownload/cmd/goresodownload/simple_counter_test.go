package main

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// TestCounterFixLogic tests the counter fix logic in isolation
func TestCounterFixLogic(t *testing.T) {
	// Create test variables
	var testProcessingCount int64
	testProcessingProps := sync.Map{}
	testTaskStatus := sync.Map{}

	// Test function that mimics fixCounterInconsistency
	testFixCounterInconsistency := func() int {
		atomicCount := atomic.LoadInt64(&testProcessingCount)
		
		// Count actual entries in processingProps map
		processingPropsCount := 0
		testProcessingProps.Range(func(key, value interface{}) bool {
			processingPropsCount++
			return true
		})
		
		// Count actual entries in taskStatus map
		taskStatusCount := 0
		testTaskStatus.Range(func(key, value interface{}) bool {
			taskStatusCount++
			return true
		})
		
		t.Logf("Counter check: atomic=%d, processingProps=%d, taskStatus=%d", 
			atomicCount, processingPropsCount, taskStatusCount)
		
		// If atomic counter is higher than actual entries, fix it
		if atomicCount > int64(processingPropsCount) {
			difference := atomicCount - int64(processingPropsCount)
			t.Logf("Detected inconsistency: atomic=%d > actual=%d, difference=%d", 
				atomicCount, processingPropsCount, difference)
			
			// Reset the atomic counter to match actual task count
			atomic.StoreInt64(&testProcessingCount, int64(processingPropsCount))
			newCount := atomic.LoadInt64(&testProcessingCount)
			
			t.Logf("Fixed inconsistency: old=%d, new=%d", atomicCount, newCount)
			return int(difference)
		}
		
		return 0
	}

	// Test case 1: Simulate the exact issue you encountered
	t.Log("=== Test Case 1: activeCount=7 but no actual tasks ===")
	
	// Set atomic counter to 7 (your exact issue)
	atomic.StoreInt64(&testProcessingCount, 7)
	
	// No actual tasks in the maps (simulating your situation)
	testProcessingProps = sync.Map{}
	testTaskStatus = sync.Map{}
	
	// Verify the inconsistency
	atomicCount := atomic.LoadInt64(&testProcessingCount)
	if atomicCount != 7 {
		t.Errorf("Expected atomic count 7, got %d", atomicCount)
	}
	
	// Fix the inconsistency
	fixedCount := testFixCounterInconsistency()
	
	// Should have fixed all 7
	if fixedCount != 7 {
		t.Errorf("Expected to fix 7 inconsistencies, fixed %d", fixedCount)
	}
	
	// Counter should now be 0
	newAtomicCount := atomic.LoadInt64(&testProcessingCount)
	if newAtomicCount != 0 {
		t.Errorf("Expected atomic count 0 after fix, got %d", newAtomicCount)
	}
	
	t.Logf("✅ Successfully fixed activeCount=7 issue: %d inconsistencies corrected", fixedCount)
	
	// Test case 2: Partial inconsistency
	t.Log("=== Test Case 2: Partial inconsistency ===")
	
	atomic.StoreInt64(&testProcessingCount, 5)
	testProcessingProps.Store("TASK_1", time.Now())
	testProcessingProps.Store("TASK_2", time.Now())
	
	fixedCount = testFixCounterInconsistency()
	if fixedCount != 3 { // 5 - 2 = 3
		t.Errorf("Expected to fix 3 inconsistencies, fixed %d", fixedCount)
	}
	
	if atomic.LoadInt64(&testProcessingCount) != 2 {
		t.Errorf("Expected atomic count 2 after fix, got %d", atomic.LoadInt64(&testProcessingCount))
	}
	
	// Test case 3: Already consistent
	t.Log("=== Test Case 3: Already consistent ===")
	
	fixedCount = testFixCounterInconsistency()
	if fixedCount != 0 {
		t.Errorf("Expected no fixes needed when consistent, got %d", fixedCount)
	}
	
	t.Log("✅ All counter fix logic tests passed")
}

// TestRealWorldScenario tests scenarios that might occur in production
func TestRealWorldScenario(t *testing.T) {
	var testProcessingCount int64
	testProcessingProps := sync.Map{}
	
	// Simulate what might happen in your production environment
	scenarios := []struct {
		name         string
		atomicCount  int64
		actualTasks  []string
		expectedFix  int
	}{
		{
			name:        "Your exact issue: activeCount=7, no tasks",
			atomicCount: 7,
			actualTasks: []string{}, // No actual tasks
			expectedFix: 7,
		},
		{
			name:        "Partial leak: some tasks finished but counter not decremented",
			atomicCount: 10,
			actualTasks: []string{"TASK_1", "TASK_2", "TASK_3"},
			expectedFix: 7, // 10 - 3 = 7
		},
		{
			name:        "Single task leak",
			atomicCount: 1,
			actualTasks: []string{},
			expectedFix: 1,
		},
		{
			name:        "No inconsistency",
			atomicCount: 3,
			actualTasks: []string{"TASK_A", "TASK_B", "TASK_C"},
			expectedFix: 0,
		},
	}
	
	for _, scenario := range scenarios {
		t.Logf("=== Scenario: %s ===", scenario.name)
		
		// Setup
		atomic.StoreInt64(&testProcessingCount, scenario.atomicCount)
		testProcessingProps = sync.Map{}
		for _, taskID := range scenario.actualTasks {
			testProcessingProps.Store(taskID, time.Now())
		}
		
		// Count actual tasks
		actualCount := 0
		testProcessingProps.Range(func(key, value interface{}) bool {
			actualCount++
			return true
		})
		
		t.Logf("Before fix: atomic=%d, actual=%d", scenario.atomicCount, actualCount)
		
		// Apply fix logic
		atomicCount := atomic.LoadInt64(&testProcessingCount)
		var fixedCount int
		if atomicCount > int64(actualCount) {
			difference := atomicCount - int64(actualCount)
			atomic.StoreInt64(&testProcessingCount, int64(actualCount))
			fixedCount = int(difference)
		}
		
		t.Logf("After fix: atomic=%d, fixed=%d", atomic.LoadInt64(&testProcessingCount), fixedCount)
		
		// Verify
		if fixedCount != scenario.expectedFix {
			t.Errorf("Scenario '%s': expected to fix %d, actually fixed %d", 
				scenario.name, scenario.expectedFix, fixedCount)
		}
		
		if atomic.LoadInt64(&testProcessingCount) != int64(len(scenario.actualTasks)) {
			t.Errorf("Scenario '%s': expected final count %d, got %d", 
				scenario.name, len(scenario.actualTasks), atomic.LoadInt64(&testProcessingCount))
		}
	}
	
	t.Log("✅ All real-world scenario tests passed")
}
