package main

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// TestTaskStageTracking tests the new task stage tracking mechanism
func TestTaskStageTracking(t *testing.T) {
	// Reset counters
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	propID := "TEST_PROP_123"

	// Test starting processing
	startProcessingProp(propID)
	
	if count := atomic.LoadInt64(&processingCount); count != 1 {
		t.<PERSON>rf("Expected processing count 1, got %d", count)
	}

	// Check initial stage
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		if status.Stage != StageAnalyzing {
			t.<PERSON>rf("Expected stage %s, got %s", StageAnalyzing, status.Stage)
		}
	} else {
		t.Error("Task status not found")
	}

	// Test stage updates
	stages := []string{StageDownloading, StageUpdatingDB, StageRemovingQueue}
	for _, stage := range stages {
		updateTaskStage(propID, stage)
		
		if statusInterface, exists := taskStatus.Load(propID); exists {
			status := statusInterface.(*TaskStatus)
			if status.Stage != stage {
				t.<PERSON>rrorf("Expected stage %s, got %s", stage, status.Stage)
			}
		} else {
			t.Error("Task status not found after update")
		}
	}

	// Test completion check - should not be complete yet
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be fully complete yet")
	}

	// Test finishing processing
	finishProcessingProp(propID)
	
	if count := atomic.LoadInt64(&processingCount); count != 0 {
		t.Errorf("Expected processing count 0, got %d", count)
	}

	// Check final stage
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		if status.Stage != StageCompleted {
			t.Errorf("Expected stage %s, got %s", StageCompleted, status.Stage)
		}
	} else {
		t.Error("Task status not found after completion")
	}

	// Test completion check - should be complete now
	if !isAllTasksFullyComplete() {
		t.Error("Tasks should be fully complete now")
	}

	t.Log("Task stage tracking test passed")
}

// TestGracefulShutdownWithStages tests graceful shutdown with stage tracking
func TestGracefulShutdownWithStages(t *testing.T) {
	// Reset counters
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	// Simulate multiple tasks in different stages
	tasks := []struct {
		propID string
		stage  string
	}{
		{"PROP_1", StageDownloading},
		{"PROP_2", StageUpdatingDB},
		{"PROP_3", StageRemovingQueue},
	}

	// Start all tasks
	for _, task := range tasks {
		startProcessingProp(task.propID)
		updateTaskStage(task.propID, task.stage)
	}

	// Should not be complete
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be complete with active stages")
	}

	// Complete tasks one by one
	for _, task := range tasks {
		finishProcessingProp(task.propID)
		
		// Check status
		count, props := getProcessingStatus()
		t.Logf("After completing %s: count=%d, props=%v", task.propID, count, props)
	}

	// Should be complete now
	if !isAllTasksFullyComplete() {
		t.Error("All tasks should be complete now")
	}

	t.Log("Graceful shutdown with stages test passed")
}

// TestForceCleanupStaleTasks tests the force cleanup functionality
func TestForceCleanupStaleTasks(t *testing.T) {
	// Reset counters
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	// Create some tasks with different ages
	now := time.Now()
	tasks := []struct {
		propID string
		age    time.Duration
	}{
		{"STALE_1", 10 * time.Minute}, // Old task
		{"STALE_2", 2 * time.Minute},  // Newer task
		{"FRESH_1", 30 * time.Second}, // Fresh task
	}

	// Start tasks with backdated start times
	for _, task := range tasks {
		startProcessingProp(task.propID)

		// Manually set the start time to simulate age
		if statusInterface, exists := taskStatus.Load(task.propID); exists {
			status := statusInterface.(*TaskStatus)
			status.StartTime = now.Add(-task.age)
			status.LastUpdate = now.Add(-task.age)
			taskStatus.Store(task.propID, status)
		}
	}

	// Verify initial state
	if count := atomic.LoadInt64(&processingCount); count != 3 {
		t.Errorf("Expected processing count 3, got %d", count)
	}

	// Clean up tasks older than 5 minutes
	cleanedCount := forceCleanupStaleTasks(5 * time.Minute)

	// Should have cleaned up STALE_1 (10 minutes old)
	if cleanedCount != 1 {
		t.Errorf("Expected to clean 1 task, cleaned %d", cleanedCount)
	}

	// Check remaining count
	if count := atomic.LoadInt64(&processingCount); count != 2 {
		t.Errorf("Expected processing count 2 after cleanup, got %d", count)
	}

	// Clean up all remaining tasks
	cleanedCount = forceCleanupStaleTasks(0)
	if cleanedCount != 2 {
		t.Errorf("Expected to clean 2 remaining tasks, cleaned %d", cleanedCount)
	}

	// Should be empty now
	if count := atomic.LoadInt64(&processingCount); count != 0 {
		t.Errorf("Expected processing count 0 after full cleanup, got %d", count)
	}

	if !isAllTasksFullyComplete() {
		t.Error("All tasks should be complete after cleanup")
	}

	t.Log("Force cleanup stale tasks test passed")
}
