package main

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/goresodownload"
	"go.mongodb.org/mongo-driver/bson"
)

// Queue processing constants
const (
	// Default priority fallback
	DefaultQueuePriority = 1000 // Default priority when calculation fails



	// Worker processing constants
	WorkerChannelBuffer = 2                           // Buffer size for worker channels
	ChannelWaitInterval = 100 * time.Millisecond     // Wait time when worker channel is full

	// Memory and resource management constants
	MemoryThresholdMB    = 800                       // Memory threshold in MB before forcing cleanup
	BytesToMB            = 1024 * 1024               // Bytes to MB conversion factor
	GoroutineThreshold   = 300                       // Goroutine count threshold before forcing cleanup
	CleanupWaitInterval  = 5 * time.Second           // Wait time after cleanup
	RetryWaitInterval    = 5 * time.Second           // Wait time before retrying operations
	QueueCheckInterval   = 5 * time.Second           // Interval for checking queue when empty

	// Task processing stage constants
	StageAnalyzing     = "analyzing"
	StageDownloading   = "downloading"
	StageUpdatingDB    = "updating_db"
	StageRemovingQueue = "removing_queue"
	StageCompleted     = "completed"
)

// Global worker pool for queue processing
var (
	queueWorkerChan chan *goresodownload.QueueItem
	queueWorkerWg   sync.WaitGroup
	workersStarted  bool
	workersMutex    sync.Mutex
)

// Processing status tracking
var (
	processingProps sync.Map // propID -> startTime
	processingCount int64    // atomic counter for active processing

	// Detailed task status tracking for graceful shutdown
	taskStatus sync.Map // propID -> TaskStatus
)

// TaskStatus represents the current status of a task
type TaskStatus struct {
	PropID      string
	StartTime   time.Time
	Stage       string // "analyzing", "downloading", "updating_db", "removing_queue", "completed"
	LastUpdate  time.Time
}



// Processing status tracking functions
func startProcessingProp(propID string) {
	atomic.AddInt64(&processingCount, 1)
	processingProps.Store(propID, time.Now())

	// Initialize detailed task status
	status := &TaskStatus{
		PropID:     propID,
		StartTime:  time.Now(),
		Stage:      StageAnalyzing,
		LastUpdate: time.Now(),
	}
	taskStatus.Store(propID, status)


}

func updateTaskStage(propID string, stage string) {
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		status.Stage = stage
		status.LastUpdate = time.Now()
		taskStatus.Store(propID, status)

	}
}

func finishProcessingProp(propID string) {
	golog.Debug("Attempting to finish processing prop", "propID", propID)

	if _, exists := processingProps.LoadAndDelete(propID); exists {
		newCount := atomic.AddInt64(&processingCount, -1)
		golog.Debug("Decremented processing count", "propID", propID, "newCount", newCount)

		// Mark task as completed
		updateTaskStage(propID, StageCompleted)

		// Immediately clean up task status to ensure isAllTasksFullyComplete() can accurately detect completion
		// This is critical for graceful shutdown accuracy
		taskStatus.Delete(propID)
		golog.Debug("Cleaned up task status", "propID", propID, "remainingCount", newCount)
	} else {
		golog.Warn("Attempted to finish processing prop that was not being tracked", "propID", propID)

		// Check if it exists in taskStatus but not in processingProps (potential inconsistency)
		if statusInterface, exists := taskStatus.Load(propID); exists {
			status := statusInterface.(*TaskStatus)
			golog.Warn("Task exists in taskStatus but not in processingProps - cleaning up",
				"propID", propID, "stage", status.Stage, "duration", time.Since(status.StartTime))
			taskStatus.Delete(propID)
		}
	}
}

func getProcessingStatus() (int64, []string) {
	count := atomic.LoadInt64(&processingCount)
	var props []string

	// Get detailed status from taskStatus map
	taskStatus.Range(func(key, value interface{}) bool {
		propID := key.(string)
		status := value.(*TaskStatus)
		duration := time.Since(status.StartTime).Seconds()
		props = append(props, fmt.Sprintf("%s[%s](%.1fs)", propID, status.Stage, duration))
		return true
	})

	// Fallback to basic status if detailed status is not available
	if len(props) == 0 {
		processingProps.Range(func(key, value interface{}) bool {
			propID := key.(string)
			startTime := value.(time.Time)
			props = append(props, fmt.Sprintf("%s(%.1fs)", propID, time.Since(startTime).Seconds()))
			return true
		})
	}

	return count, props
}

// isAllTasksFullyComplete checks if all tasks have completed all stages including DB updates
func isAllTasksFullyComplete() bool {
	// First check atomic counter
	activeCount := atomic.LoadInt64(&processingCount)
	if activeCount > 0 {
		golog.Debug("Tasks still active according to atomic counter", "activeCount", activeCount)
		return false
	}

	// Check if any tasks are still in non-completed state
	allCompleted := true
	incompleteCount := 0
	var incompleteTasks []string

	taskStatus.Range(func(key, value interface{}) bool {
		propID := key.(string)
		status := value.(*TaskStatus)
		if status.Stage != StageCompleted {
			allCompleted = false
			incompleteCount++
			incompleteTasks = append(incompleteTasks, fmt.Sprintf("%s[%s]", propID, status.Stage))
			golog.Debug("Found incomplete task", "propID", propID, "stage", status.Stage, "duration", time.Since(status.StartTime))
			// Continue checking all tasks, don't return early
		}
		return true
	})

	if incompleteCount > 0 {
		golog.Debug("Tasks still incomplete according to detailed status", "incompleteCount", incompleteCount, "tasks", incompleteTasks)
	}

	return allCompleted
}

// forceCleanupStaleTasks removes tasks that have been running for too long (potential stuck tasks)
func forceCleanupStaleTasks(maxDuration time.Duration) int {
	cleanedCount := 0
	var staleTasks []string

	taskStatus.Range(func(key, value interface{}) bool {
		propID := key.(string)
		status := value.(*TaskStatus)

		// If task has been running for more than maxDuration, consider it stale
		if time.Since(status.StartTime) > maxDuration {
			staleTasks = append(staleTasks, propID)
			golog.Warn("Found stale task - forcing cleanup",
				"propID", propID,
				"stage", status.Stage,
				"duration", time.Since(status.StartTime),
				"startTime", status.StartTime)
		}
		return true
	})

	// Clean up stale tasks
	for _, propID := range staleTasks {
		// Remove from both maps
		if _, exists := processingProps.LoadAndDelete(propID); exists {
			atomic.AddInt64(&processingCount, -1)
			cleanedCount++
		}
		taskStatus.Delete(propID)
		golog.Info("Force cleaned up stale task", "propID", propID)
	}

	if cleanedCount > 0 {
		golog.Info("Force cleanup completed", "cleanedCount", cleanedCount, "remainingCount", atomic.LoadInt64(&processingCount))
	}

	return cleanedCount
}

// debugTaskStatus logs detailed information about current task status for debugging
func debugTaskStatus() {
	atomicCount := atomic.LoadInt64(&processingCount)
	golog.Info("=== Task Status Debug ===", "atomicCount", atomicCount)

	// Check processingProps map
	processingPropsCount := 0
	processingProps.Range(func(key, value interface{}) bool {
		propID := key.(string)
		startTime := value.(time.Time)
		processingPropsCount++
		golog.Info("ProcessingProps entry", "propID", propID, "startTime", startTime, "duration", time.Since(startTime))
		return true
	})
	golog.Info("ProcessingProps map", "count", processingPropsCount)

	// Check taskStatus map
	taskStatusCount := 0
	taskStatus.Range(func(key, value interface{}) bool {
		propID := key.(string)
		status := value.(*TaskStatus)
		taskStatusCount++
		golog.Info("TaskStatus entry", "propID", propID, "stage", status.Stage, "startTime", status.StartTime, "duration", time.Since(status.StartTime))
		return true
	})
	golog.Info("TaskStatus map", "count", taskStatusCount)

	golog.Info("=== End Task Status Debug ===")
}

// ProcessQueueItem processes a single queue item (the actual analysis and download logic)
func ProcessQueueItem(queueItem *goresodownload.QueueItem) error {

	// Track processing start
	startProcessingProp(queueItem.ID)
	defer func() {
		golog.Debug("ProcessQueueItem defer cleanup", "propID", queueItem.ID)
		finishProcessingProp(queueItem.ID)
	}()



	// Add panic recovery - this runs before the main defer
	defer func() {
		if r := recover(); r != nil {
			golog.Error("Panic in ProcessQueueItem", "propId", queueItem.ID, "panic", r)
			// The main defer will still run and clean up the task
		}
	}()

	// Step 1: Get property data
	newProp, err := goresodownload.GetNewPropFromWatchTable(queueItem.ID, gBoardType)
	if err != nil {
		golog.Error("Failed to get change doc from watch table", "propId", queueItem.ID, "error", err)
		return fmt.Errorf("failed to get change doc from watch table: %w", err)
	}

	// Step 2: Analyze changes
	result, err := analyzer.Analyze(newProp, gBoardType)
	if err != nil {
		speedMeter.Check("analyzeError", 1)
		golog.Error("Failed to analyze changes", "propId", queueItem.ID, "error", err)
		return fmt.Errorf("failed to analyze changes: %w", err)
	}


	// Step 3: Download and delete media files - critical step, must complete all image downloads
	updateTaskStage(queueItem.ID, StageDownloading)


	tnChangedNum, downloadErr := downloader.ProcessAnalysisResult(&result, gBoardType)

	if downloadErr != nil {
		speedMeter.Check("downloadError", 1)
		golog.Error("ProcessAnalysisResult failed", "propID", queueItem.ID, "error", downloadErr)
		// Continue with subsequent steps even if download fails to ensure data consistency
	}

	// Step 4: Update directory storage statistics (execute even if download failed)
	updateTaskStage(queueItem.ID, StageUpdatingDB)
	dirStoreErr := updateDirStoreStats(result, dirStore, tnChangedNum)
	if dirStoreErr != nil {
		speedMeter.Check("updateDirStoreError", 1)
		golog.Error("Failed to update dirStore stats", "propID", queueItem.ID, "error", dirStoreErr)
		// Continue processing but record the error
	}



	speedMeter.Check("prop", 1)
	speedMeter.Check("downloadMedia", float64(len(result.DownloadTasks)))
	speedMeter.Check("deleteMedia", float64(len(result.DeleteTasks)))

	// Return the first error encountered (prioritize download error)
	if downloadErr != nil {
		return fmt.Errorf("processing failed: %w", downloadErr)
	}
	if dirStoreErr != nil {
		return fmt.Errorf("failed to update dirStore stats: %w", dirStoreErr)
	}

	return nil
}

// startQueueWorkers starts fixed number of worker goroutines
func startQueueWorkers(ctx context.Context, numWorkers int) {
	workersMutex.Lock()
	defer workersMutex.Unlock()

	// Prevent multiple calls - only start workers once
	if workersStarted {

		return
	}

	queueWorkerChan = make(chan *goresodownload.QueueItem, WorkerChannelBuffer)
	workersStarted = true



	for i := 0; i < numWorkers; i++ {
		queueWorkerWg.Add(1)
		go func(workerID int) {
			defer queueWorkerWg.Done()


			for {
				select {
				case <-ctx.Done():

					return
				case item, ok := <-queueWorkerChan:
					if !ok {
						return
					}

					// Process the item with panic recovery and ensure completion
					func() {
						defer func() {
							if r := recover(); r != nil {
								golog.Error("Panic in queue worker", "workerID", workerID, "propId", item.ID, "panic", r)
								// Even if panic occurs, ensure task status is properly cleaned up
								// Note: ProcessQueueItem has its own defer that should handle cleanup,
								// but we add this as a safety net in case the panic happens before that
								golog.Warn("Ensuring cleanup after panic", "propId", item.ID)
								finishProcessingProp(item.ID)
							}
						}()



						// Check if shutdown signal has been received
						select {
						case <-ctx.Done():
							golog.Warn("Shutdown signal received while processing item - will complete ALL images for current prop",
								"workerID", workerID, "propId", item.ID)
							// Important: Even if shutdown signal is received, complete all image downloads for current prop
						default:
							// Normal processing
						}



						// Execute complete processing flow: analyze, download all images, update database
						// Critical: Regardless of shutdown signal, complete all image downloads for current prop
						err := ProcessQueueItem(item)
						if err != nil {
							// Improved error logging with more details
							errorMsg := "unknown error"
							if err != nil {
								errorMsg = err.Error()
								if errorMsg == "" {
									errorMsg = fmt.Sprintf("empty error of type %T", err)
								}
							}
							golog.Error("Failed to process queue item",
								"workerID", workerID,
								"propId", item.ID,
								"error", err,
								"errorMsg", errorMsg,
								"errorType", fmt.Sprintf("%T", err))
						}

						// Step 5: Remove item from queue (execute regardless of processing success)
						// This is the final step of processing flow, ensures queue doesn't accumulate
						updateTaskStage(item.ID, StageRemovingQueue)


						// Remove item from queue - simple MongoDB delete operation
						if removeErr := downloadQueue.RemoveFromQueue(item); removeErr != nil {
							golog.Error("Failed to remove processed item from queue",
								"workerID", workerID,
								"propId", item.ID,
								"error", removeErr)
							// Continue anyway - the item was processed successfully
						}
					}()
				}
			}
		}(i)
	}
}

// stopQueueWorkers gracefully stops all queue workers
func stopQueueWorkers() {
	workersMutex.Lock()
	defer workersMutex.Unlock()

	if !workersStarted {
		golog.Debug("Queue workers not started, nothing to stop")
		return
	}



	// First stop accepting new tasks
	if queueWorkerChan != nil {
		close(queueWorkerChan)
		queueWorkerChan = nil
	}

	// Wait for all workers to complete current tasks
	queueWorkerWg.Wait()

	workersStarted = false
}

// ProcessQueue continuously processes items from the download queue
func ProcessQueue(ctx context.Context) {
	golog.Info("Starting queue processor")

	startQueueWorkers(ctx, batchSize)

	// Ensure workers are stopped when function exits
	defer func() {
		stopQueueWorkers()
	}()

	for {
		select {
		case <-ctx.Done():
			golog.Info("Queue processor stopping")
			return
		default:
			// Check memory pressure and goroutine count before processing
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			numGoroutines := runtime.NumGoroutine()

			// More lenient memory threshold and goroutine limit
			if m.Alloc > MemoryThresholdMB*BytesToMB || numGoroutines > GoroutineThreshold {
				golog.Warn("High resource usage detected, forcing cleanup",
					"currentAlloc", formatBytes(m.Alloc), "numGoroutines", numGoroutines)
				runtime.GC()                    // Force GC
				time.Sleep(CleanupWaitInterval) // Shorter wait time
				// Continue processing anyway to avoid deadlock
			}

			// During shutdown, no longer retrieve new tasks
			select {
			case <-ctx.Done():
				return
			default:
				// Continue normal processing
			}

			// Get next item from queue
			item, err := downloadQueue.GetNext(gBoardType)
			if err != nil {
				golog.Error("Failed to get next item from queue", "error", err)
				time.Sleep(RetryWaitInterval) // Wait before retrying
				continue
			}

			if item == nil {
				// No items to process, wait before checking again
				time.Sleep(QueueCheckInterval)
				continue
			}

	

			// Send item to worker pool - must ensure every task is sent, no limits
			sent := false
			sendAttempts := 0

			for !sent {
				select {
				case queueWorkerChan <- item:
					// Item sent to worker successfully
					sent = true
					golog.Info("Item sent to worker", "propId", item.ID)
				case <-ctx.Done():
					golog.Info("Queue processor stopping while sending item")
					return
				case <-time.After(ChannelWaitInterval):
					// Channel is full, keep waiting - we must process every task
					sendAttempts++
				}
			}
		}
	}
}

// updateDirStoreStats updates the directory store statistics
func updateDirStoreStats(result goresodownload.AnalysisResult, dirStore *levelStore.DirKeyStore, tnChangedNum int) error {
	l1, l2, err := levelStore.GetL1L2Separate(result.PropTs, gBoardType, result.Sid)
	if err != nil {
		return fmt.Errorf("failed to get l1 and l2: %w", err)
	}
	golog.Info("updateDirStoreStats", "l1", l1, "propTs", result.PropTs, "l2", l2, "sid", result.Sid)

	// Update stats
	mediaAmount := len(result.DownloadTasks) - len(result.DeleteTasks) + tnChangedNum
	if mediaAmount != 0 {
		dirStore.AddDirStats(l1, l2, 1, mediaAmount) // 1 means one property
	}
	golog.Info("updated dirStore stats", "l1", l1, "l2", l2, "mediaAmount", mediaAmount)

	return nil
}

// getPriority calculates the priority for a queue item
func getPriority(prop bson.M) int {
	// Extract property ID from prop
	propID, ok := prop["_id"].(string)
	if !ok {
		golog.Error("Failed to extract property ID from prop")
		return DefaultQueuePriority // fallback to default priority
	}

	// Fetch existing merged property
	existMergedProp, err := goresodownload.GetExistingMergedProperty(propID, gBoardType)
	if err != nil {
		golog.Error("Failed to fetch existing merged property", "propId", propID, "error", err)
		return DefaultQueuePriority // fallback to default priority
	}

	// Calculate priority using the new priority calculator
	priority, err := goresodownload.CalculatePriority(gBoardType, existMergedProp)
	if err != nil {
		golog.Error("Failed to calculate priority", "propId", propID, "error", err)
		return DefaultQueuePriority // fallback to default priority
	}

	golog.Debug("Calculated priority", "propId", propID, "priority", priority, "boardType", gBoardType)

	return priority
}
