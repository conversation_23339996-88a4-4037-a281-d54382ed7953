/*
###
Description:    Batch add properties without phoLH to download queue

Usage:         ./start.sh  -n addPhoP -d "goresodownload" -cmd "cmd/batch/addPhoP/main.go -board=TRB -dryrun"

Create date:    2025-07-21
Author:         Maggie
Run frequency:  As needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"sync"
	"time"

	goconfig "github.com/real-rm/goconfig"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag  = flag.String("board", "TRB", "Board name for file path generation")
	speedMeter *gospeedmeter.SpeedMeter
	speedMutex sync.Mutex
	startTime  = time.Now()
)

// BoardMergedTable maps board types to their merged collection names
var BoardMergedTable = map[string]string{
	"CAR": "mls_car_master_records",
	"DDF": "reso_crea_merged",
	"BRE": "bridge_bcre_merged",
	"EDM": "mls_rae_master_records",
	"TRB": "reso_treb_evow_merged",
}

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

func processProperties(ctx context.Context) error {
	// Parse command line flags
	flag.Parse()
	golog.Info("Starting addPhoP batch processing", "dryrun", *dryrunFlag, "board", *boardFlag)

	// Validate board flag
	collectionName, exists := BoardMergedTable[*boardFlag]
	if !exists {
		return fmt.Errorf("invalid board: %s. Valid boards: CAR, DDF, BRE, EDM, TRB", *boardFlag)
	}

	// Get collection using BoardMergedTable
	coll := gomongo.Coll("rni", collectionName)
	golog.Info("Processing collection", "board", *boardFlag, "collection", collectionName, "dryrun", *dryrunFlag)

	// Query all documents (filter will be applied in processing)
	query := bson.M{}

	options := gomongo.QueryOptions{
		Projection: bson.D{
			{Key: "_id", Value: 1},
			{Key: "ts", Value: 1},
			{Key: "ListingKey", Value: 1},
			{Key: "phoLH", Value: 1},
			{Key: "phoP", Value: 1},
		},
		// Remove sort for better performance
	}

	// Get cursor
	golog.Info("Executing query", "query", query)
	cursor, err := coll.Find(ctx, query, options)
	if err != nil {
		golog.Error("Failed to execute query", "error", err)
		return fmt.Errorf("failed to execute query: %v", err)
	}
	golog.Info("Query executed successfully, starting streaming")

	opts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			return processItem(ctx, coll, item)
		},
		End: func(err error) {
			duration := time.Since(startTime)
			fmt.Println("Total process time:", duration)
			speedMutex.Lock()
			if err != nil {
				golog.Error("Stream ended with error", "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
			speedMutex.Unlock()
		},
		Error: func(err error) {
			golog.Error("Processing error", "error", err)
		},
		High:    10,
		Verbose: 2,
	}

	golog.Info("Starting gostreaming.Streaming")
	err = gostreaming.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "error", err)
		return err
	}
	golog.Info("Streaming completed successfully")
	return nil
}

// processItem processes a single document to add phoP field
func processItem(ctx context.Context, coll *gomongo.MongoCollection, item interface{}) error {
	golog.Debug("Processing item started")

	// Track processing speed
	speedMeter.Check("processed", 1)

	// Convert item to bson.M (handle both bson.M and bson.D types)
	var doc bson.M
	switch v := item.(type) {
	case bson.M:
		doc = v
	case bson.D:
		// Convert bson.D to bson.M using marshal/unmarshal
		data, err := bson.Marshal(v)
		if err != nil {
			golog.Error("Failed to marshal bson.D", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to marshal bson.D: %v", err)
		}
		if err := bson.Unmarshal(data, &doc); err != nil {
			golog.Error("Failed to unmarshal to bson.M", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to unmarshal to bson.M: %v", err)
		}
	default:
		golog.Error("Unsupported document type", "type", fmt.Sprintf("%T", item), "item", item)
		speedMeter.Check("errors", 1)
		return fmt.Errorf("unsupported document type: %T", item)
	}

	golog.Debug("Item converted to bson.M successfully")

	// Check if document has phoLH but no phoP
	phoLH, hasPhoLH := doc["phoLH"]
	phoP, hasPhoP := doc["phoP"]

	// Skip if no phoLH
	if (!hasPhoLH) || (phoLH == nil) {
		golog.Debug("Skipping document: no phoLH", "_id", doc["_id"])
		speedMeter.Check("skipped_no_phoLH", 1)
		return nil
	}

	// Skip if phoP already exists and is not empty
	if hasPhoP && (phoP != nil) && (phoP != "") {
		golog.Debug("Skipping document: phoP already exists", "_id", doc["_id"], "phoP", phoP)
		speedMeter.Check("skipped_has_phoP", 1)
		return nil
	}

	golog.Debug("Document needs phoP update", "_id", doc["_id"])

	// Extract required fields
	id, ok := doc["_id"].(string)
	if !ok {
		golog.Error("Failed to extract _id", "doc", doc)
		speedMeter.Check("idErrors", 1)
		return fmt.Errorf("failed to extract _id")
	}

	listingKey, ok := doc["ListingKey"].(string)
	if !ok {
		golog.Error("Failed to extract ListingKey", "doc", doc, "_id", id)
		speedMeter.Check("listingKeyErrors", 1)
		return fmt.Errorf("failed to extract ListingKey")
	}

	// Extract timestamp
	var ts time.Time
	switch v := doc["ts"].(type) {
	case primitive.DateTime:
		ts = v.Time()
	case time.Time:
		ts = v
	default:
		golog.Error("Failed to extract ts", "doc", doc, "_id", id, "ts_type", fmt.Sprintf("%T", v))
		speedMeter.Check("tsErrors", 1)
		return fmt.Errorf("failed to extract ts")
	}

	// Get full file path using levelStore
	filePath, err := levelStore.GetFullFilePathForProp(ts, *boardFlag, listingKey)
	if err != nil {
		golog.Error("Failed to get full file path",
			"_id", id,
			"listingKey", listingKey,
			"ts", ts,
			"error", err)
		speedMeter.Check("filePathErrors", 1)
		return fmt.Errorf("failed to get full file path: %w", err)
	}

	// Check if this is a dry run
	if *dryrunFlag {
		// Use Debug level to reduce log noise, only log every 100th item
		speedMeter.Check("dryrun", 1)

		golog.Info("Dry run mode: Would update document",
			"_id", id,
			"listingKey", listingKey,
			"filePath", filePath)
		return nil
	}

	// Update document with phoP field
	update := bson.M{
		"$set": bson.M{
			"phoP": filePath,
		},
	}

	result, err := coll.UpdateOne(ctx, bson.M{"_id": id}, update)
	if err != nil {
		golog.Error("Failed to update document",
			"_id", id,
			"filePath", filePath,
			"error", err)
		speedMeter.Check("updateErrors", 1)
		return fmt.Errorf("failed to update document: %w", err)
	}

	// Track successful updates
	speedMeter.Check("updated", 1)

	golog.Info("Successfully updated document",
		"_id", id,
		"listingKey", listingKey,
		"filePath", filePath,
		"modifiedCount", result.ModifiedCount)

	return nil
}

func main() {
	// Create context with timeout to prevent hanging
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	golog.Info("Starting addPhoP batch process")
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
	golog.Info("addPhoP batch process completed successfully")
}
